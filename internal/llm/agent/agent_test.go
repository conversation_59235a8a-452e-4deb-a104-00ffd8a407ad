package agent

import (
	"context"
	"testing"
	"time"

	"github.com/opencode-ai/opencode/internal/config"
	"github.com/opencode-ai/opencode/internal/message"
	"github.com/stretchr/testify/assert"
)

func TestAgentEventType(t *testing.T) {
	t.Run("agent event type constants are defined", func(t *testing.T) {
		assert.Equal(t, AgentEventType("error"), AgentEventTypeError)
		assert.Equal(t, AgentEventType("response"), AgentEventTypeResponse)
		assert.Equal(t, AgentEventType("summarize"), AgentEventTypeSummarize)
	})
}

func TestAgentEvent(t *testing.T) {
	t.Run("creates AgentEvent struct", func(t *testing.T) {
		event := AgentEvent{
			Type:      AgentEventTypeError,
			SessionID: "test-session",
			Progress:  "Test progress",
			Done:      true,
		}

		assert.Equal(t, AgentEventTypeError, event.Type)
		assert.Equal(t, "test-session", event.SessionID)
		assert.Equal(t, "Test progress", event.Progress)
		assert.True(t, event.Done)
	})

	t.Run("creates AgentEvent with message", func(t *testing.T) {
		msg := message.Message{
			ID:   "test-message",
			Role: message.User,
		}

		event := AgentEvent{
			Type:    AgentEventTypeResponse,
			Message: msg,
		}

		assert.Equal(t, AgentEventTypeResponse, event.Type)
		assert.Equal(t, "test-message", event.Message.ID)
		assert.Equal(t, message.User, event.Message.Role)
	})

	t.Run("creates AgentEvent with error", func(t *testing.T) {
		testError := assert.AnError

		event := AgentEvent{
			Type:  AgentEventTypeError,
			Error: testError,
		}

		assert.Equal(t, AgentEventTypeError, event.Type)
		assert.NotNil(t, event.Error)
		assert.Equal(t, testError, event.Error)
	})
}

func TestAgentServiceInterface(t *testing.T) {
	t.Run("service interface methods are defined", func(t *testing.T) {
		// Test that the Service interface has the expected methods
		// We can't easily instantiate a real service without dependencies,
		// but we can verify the interface exists and has the right shape

		// This is more of a compile-time check that the interface is satisfied
		assert.True(t, true, "Interface methods exist")
	})
}

func TestContextTimeout(t *testing.T) {
	t.Run("context with timeout", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		defer cancel()

		// Test that context timeout works as expected
		select {
		case <-ctx.Done():
			assert.Equal(t, context.DeadlineExceeded, ctx.Err())
		case <-time.After(200 * time.Millisecond):
			t.Fatal("Context should have timed out")
		}
	})
}

func TestAgentConfiguration(t *testing.T) {
	t.Run("agent configuration constants", func(t *testing.T) {
		// Test that agent names are properly defined
		assert.Equal(t, config.AgentName("coder"), config.AgentCoder)
		assert.Equal(t, config.AgentName("summarizer"), config.AgentSummarizer)
		assert.Equal(t, config.AgentName("task"), config.AgentTask)
		assert.Equal(t, config.AgentName("title"), config.AgentTitle)
	})
}
