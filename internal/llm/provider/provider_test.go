package provider

import (
	"context"
	"testing"

	"github.com/opencode-ai/opencode/internal/llm/models"
	"github.com/opencode-ai/opencode/internal/llm/tools"
	"github.com/opencode-ai/opencode/internal/message"
	"github.com/stretchr/testify/assert"
)

func TestEventType(t *testing.T) {
	t.Run("event type constants are defined", func(t *testing.T) {
		assert.Equal(t, EventType("content_start"), EventContentStart)
		assert.Equal(t, EventType("content_delta"), EventContentDelta)
		assert.Equal(t, EventType("thinking_delta"), EventThinkingDelta)
		assert.Equal(t, EventType("tool_use_start"), EventToolUseStart)
		assert.Equal(t, EventType("tool_use_delta"), EventToolUseDelta)
		assert.Equal(t, EventType("tool_use_stop"), EventToolUseStop)
		assert.Equal(t, EventType("content_stop"), EventContentStop)
		assert.Equal(t, EventType("complete"), EventComplete)
		assert.Equal(t, EventType("error"), EventError)
		assert.Equal(t, EventType("warning"), EventWarning)
	})
}

func TestTokenUsage(t *testing.T) {
	t.Run("creates TokenUsage struct", func(t *testing.T) {
		usage := TokenUsage{
			InputTokens:         100,
			OutputTokens:        50,
			CacheCreationTokens: 10,
			CacheReadTokens:     5,
		}

		assert.Equal(t, int64(100), usage.InputTokens)
		assert.Equal(t, int64(50), usage.OutputTokens)
		assert.Equal(t, int64(10), usage.CacheCreationTokens)
		assert.Equal(t, int64(5), usage.CacheReadTokens)
	})
}

func TestProviderResponse(t *testing.T) {
	t.Run("creates ProviderResponse struct", func(t *testing.T) {
		toolCalls := []message.ToolCall{
			{
				ID:    "test-tool-call",
				Name:  "test-tool",
				Input: `{"param": "value"}`,
			},
		}

		usage := TokenUsage{
			InputTokens:  100,
			OutputTokens: 50,
		}

		response := ProviderResponse{
			Content:      "Test response content",
			ToolCalls:    toolCalls,
			Usage:        usage,
			FinishReason: message.FinishReasonEndTurn,
		}

		assert.Equal(t, "Test response content", response.Content)
		assert.Len(t, response.ToolCalls, 1)
		assert.Equal(t, "test-tool-call", response.ToolCalls[0].ID)
		assert.Equal(t, int64(100), response.Usage.InputTokens)
		assert.Equal(t, message.FinishReasonEndTurn, response.FinishReason)
	})
}

func TestProviderEvent(t *testing.T) {
	t.Run("creates ProviderEvent struct", func(t *testing.T) {
		event := ProviderEvent{
			Type:     EventContentDelta,
			Content:  "Test content",
			Thinking: "Test thinking",
		}

		assert.Equal(t, EventContentDelta, event.Type)
		assert.Equal(t, "Test content", event.Content)
		assert.Equal(t, "Test thinking", event.Thinking)
		assert.Nil(t, event.Response)
		assert.Nil(t, event.ToolCall)
		assert.Nil(t, event.Error)
	})

	t.Run("creates ProviderEvent with response", func(t *testing.T) {
		response := &ProviderResponse{
			Content: "Test response",
		}

		event := ProviderEvent{
			Type:     EventComplete,
			Response: response,
		}

		assert.Equal(t, EventComplete, event.Type)
		assert.NotNil(t, event.Response)
		assert.Equal(t, "Test response", event.Response.Content)
	})

	t.Run("creates ProviderEvent with tool call", func(t *testing.T) {
		toolCall := &message.ToolCall{
			ID:   "test-tool",
			Name: "test-function",
		}

		event := ProviderEvent{
			Type:     EventToolUseStart,
			ToolCall: toolCall,
		}

		assert.Equal(t, EventToolUseStart, event.Type)
		assert.NotNil(t, event.ToolCall)
		assert.Equal(t, "test-tool", event.ToolCall.ID)
	})

	t.Run("creates ProviderEvent with error", func(t *testing.T) {
		testError := assert.AnError

		event := ProviderEvent{
			Type:  EventError,
			Error: testError,
		}

		assert.Equal(t, EventError, event.Type)
		assert.NotNil(t, event.Error)
		assert.Equal(t, testError, event.Error)
	})
}

func TestProviderClientOptions(t *testing.T) {
	t.Run("creates provider client options", func(t *testing.T) {
		model := models.Model{
			ID:       "test-model",
			Name:     "Test Model",
			Provider: "test-provider",
		}

		opts := providerClientOptions{
			apiKey:        "test-api-key",
			model:         model,
			maxTokens:     1000,
			systemMessage: "Test system message",
		}

		assert.Equal(t, "test-api-key", opts.apiKey)
		assert.Equal(t, model, opts.model)
		assert.Equal(t, int64(1000), opts.maxTokens)
		assert.Equal(t, "Test system message", opts.systemMessage)
	})
}

func TestWithAPIKey(t *testing.T) {
	t.Run("sets API key option", func(t *testing.T) {
		opts := providerClientOptions{}
		option := WithAPIKey("test-key")
		option(&opts)

		assert.Equal(t, "test-key", opts.apiKey)
	})
}

func TestWithModel(t *testing.T) {
	t.Run("sets model option", func(t *testing.T) {
		model := models.Model{
			ID:   "test-model",
			Name: "Test Model",
		}

		opts := providerClientOptions{}
		option := WithModel(model)
		option(&opts)

		assert.Equal(t, model, opts.model)
	})
}

func TestWithMaxTokens(t *testing.T) {
	t.Run("sets max tokens option", func(t *testing.T) {
		opts := providerClientOptions{}
		option := WithMaxTokens(2000)
		option(&opts)

		assert.Equal(t, int64(2000), opts.maxTokens)
	})
}

func TestWithSystemMessage(t *testing.T) {
	t.Run("sets system message option", func(t *testing.T) {
		opts := providerClientOptions{}
		option := WithSystemMessage("Custom system message")
		option(&opts)

		assert.Equal(t, "Custom system message", opts.systemMessage)
	})
}

func TestNewProvider(t *testing.T) {
	t.Run("returns error for unsupported provider", func(t *testing.T) {
		provider, err := NewProvider("unsupported-provider")
		assert.Error(t, err)
		assert.Nil(t, provider)
		assert.Contains(t, err.Error(), "provider not supported")
	})

	t.Run("panics for mock provider", func(t *testing.T) {
		assert.Panics(t, func() {
			NewProvider(models.ProviderMock)
		})
	})

	t.Run("creates provider for supported providers", func(t *testing.T) {
		supportedProviders := []models.ModelProvider{
			models.ProviderCopilot,
			models.ProviderAnthropic,
			models.ProviderOpenAI,
			models.ProviderGemini,
			models.ProviderBedrock,
			models.ProviderGROQ,
			models.ProviderAzure,
			models.ProviderVertexAI,
			models.ProviderOpenRouter,
			models.ProviderXAI,
			models.ProviderLocal,
		}

		for _, providerName := range supportedProviders {
			t.Run(string(providerName), func(t *testing.T) {
				model := models.Model{
					ID:       models.ModelID("test-model"),
					Provider: providerName,
				}

				provider, err := NewProvider(
					providerName,
					WithModel(model),
					WithAPIKey("test-key"),
				)

				// We expect no error for supported providers
				// Note: Some providers might fail due to missing credentials or configuration
				// but they should not return "provider not supported" error
				if err != nil {
					assert.NotContains(t, err.Error(), "provider not supported")
				} else {
					assert.NotNil(t, provider)
					assert.Equal(t, model, provider.Model())
				}
			})
		}
	})
}

func TestBaseProviderCleanMessages(t *testing.T) {
	t.Run("removes messages with no content", func(t *testing.T) {
		// Create a mock base provider for testing
		provider := &baseProvider[mockClient]{
			options: providerClientOptions{},
			client:  mockClient{},
		}

		messages := []message.Message{
			{
				Role:  message.User,
				Parts: []message.ContentPart{message.TextContent{Text: "Hello"}},
			},
			{
				Role:  message.Assistant,
				Parts: []message.ContentPart{}, // Empty parts - should be removed
			},
			{
				Role:  message.User,
				Parts: []message.ContentPart{message.TextContent{Text: "World"}},
			},
		}

		cleaned := provider.cleanMessages(messages)

		assert.Len(t, cleaned, 2)
		assert.Equal(t, "Hello", cleaned[0].Content().Text)
		assert.Equal(t, "World", cleaned[1].Content().Text)
	})

	t.Run("keeps all messages with content", func(t *testing.T) {
		provider := &baseProvider[mockClient]{
			options: providerClientOptions{},
			client:  mockClient{},
		}

		messages := []message.Message{
			{
				Role:  message.User,
				Parts: []message.ContentPart{message.TextContent{Text: "Hello"}},
			},
			{
				Role:  message.Assistant,
				Parts: []message.ContentPart{message.TextContent{Text: "Hi there"}},
			},
		}

		cleaned := provider.cleanMessages(messages)

		assert.Len(t, cleaned, 2)
		assert.Equal(t, messages, cleaned)
	})
}

// Mock client for testing
type mockClient struct{}

func (m mockClient) send(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*ProviderResponse, error) {
	return &ProviderResponse{
		Content: "Mock response",
	}, nil
}

func (m mockClient) stream(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan ProviderEvent {
	ch := make(chan ProviderEvent, 1)
	ch <- ProviderEvent{
		Type:    EventComplete,
		Content: "Mock stream response",
	}
	close(ch)
	return ch
}
