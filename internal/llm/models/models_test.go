package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestModelTypes(t *testing.T) {
	t.Run("ModelID is string type", func(t *testing.T) {
		var id ModelID = "test-model"
		assert.Equal(t, "test-model", string(id))
	})
	
	t.Run("ModelProvider is string type", func(t *testing.T) {
		var provider ModelProvider = "test-provider"
		assert.Equal(t, "test-provider", string(provider))
	})
}

func TestModelStruct(t *testing.T) {
	t.Run("creates Model struct with all fields", func(t *testing.T) {
		model := Model{
			ID:                  "test-model",
			Name:                "Test Model",
			Provider:            "test-provider",
			APIModel:            "api-model",
			CostPer1MIn:         1.0,
			CostPer1MOut:        2.0,
			CostPer1MInCached:   0.5,
			CostPer1MOutCached:  1.0,
			ContextWindow:       4096,
			DefaultMaxTokens:    1000,
			CanReason:           true,
			SupportsAttachments: true,
		}
		
		assert.Equal(t, <PERSON><PERSON>("test-model"), model.ID)
		assert.Equal(t, "Test Model", model.Name)
		assert.Equal(t, Model<PERSON>rovider("test-provider"), model.Provider)
		assert.Equal(t, "api-model", model.APIModel)
		assert.Equal(t, 1.0, model.CostPer1MIn)
		assert.Equal(t, 2.0, model.CostPer1MOut)
		assert.Equal(t, 0.5, model.CostPer1MInCached)
		assert.Equal(t, 1.0, model.CostPer1MOutCached)
		assert.Equal(t, int64(4096), model.ContextWindow)
		assert.Equal(t, int64(1000), model.DefaultMaxTokens)
		assert.True(t, model.CanReason)
		assert.True(t, model.SupportsAttachments)
	})
}

func TestProviderConstants(t *testing.T) {
	t.Run("provider constants are defined", func(t *testing.T) {
		assert.Equal(t, ModelProvider("anthropic"), ProviderAnthropic)
		assert.Equal(t, ModelProvider("openai"), ProviderOpenAI)
		assert.Equal(t, ModelProvider("gemini"), ProviderGemini)
		assert.Equal(t, ModelProvider("groq"), ProviderGROQ)
		assert.Equal(t, ModelProvider("azure"), ProviderAzure)
		assert.Equal(t, ModelProvider("bedrock"), ProviderBedrock)
		assert.Equal(t, ModelProvider("copilot"), ProviderCopilot)
		assert.Equal(t, ModelProvider("openrouter"), ProviderOpenRouter)
		assert.Equal(t, ModelProvider("vertexai"), ProviderVertexAI)
		assert.Equal(t, ModelProvider("xai"), ProviderXAI)
		assert.Equal(t, ModelProvider("local"), ProviderLocal)
		assert.Equal(t, ModelProvider("__mock"), ProviderMock)
	})
}

func TestModelIDConstants(t *testing.T) {
	t.Run("anthropic model IDs are defined", func(t *testing.T) {
		assert.Equal(t, ModelID("claude-3.5-sonnet"), Claude35Sonnet)
		assert.Equal(t, ModelID("claude-3-haiku"), Claude3Haiku)
		assert.Equal(t, ModelID("claude-3.7-sonnet"), Claude37Sonnet)
		assert.Equal(t, ModelID("claude-3.5-haiku"), Claude35Haiku)
		assert.Equal(t, ModelID("claude-3-opus"), Claude3Opus)
		assert.Equal(t, ModelID("claude-4-opus"), Claude4Opus)
		assert.Equal(t, ModelID("claude-4-sonnet"), Claude4Sonnet)
	})
	
	t.Run("openai model IDs are defined", func(t *testing.T) {
		assert.Equal(t, ModelID("gpt-4.1"), GPT41)
		assert.Equal(t, ModelID("gpt-4.1-mini"), GPT41Mini)
		assert.Equal(t, ModelID("gpt-4.1-nano"), GPT41Nano)
		assert.Equal(t, ModelID("gpt-4.5-preview"), GPT45Preview)
		assert.Equal(t, ModelID("gpt-4o"), GPT4o)
		assert.Equal(t, ModelID("gpt-4o-mini"), GPT4oMini)
		assert.Equal(t, ModelID("o1"), O1)
		assert.Equal(t, ModelID("o1-pro"), O1Pro)
		assert.Equal(t, ModelID("o1-mini"), O1Mini)
		assert.Equal(t, ModelID("o3"), O3)
		assert.Equal(t, ModelID("o3-mini"), O3Mini)
		assert.Equal(t, ModelID("o4-mini"), O4Mini)
	})
	
	t.Run("gemini model IDs are defined", func(t *testing.T) {
		assert.Equal(t, ModelID("gemini-2.5-flash"), Gemini25Flash)
		assert.Equal(t, ModelID("gemini-2.5"), Gemini25)
		assert.Equal(t, ModelID("gemini-2.0-flash"), Gemini20Flash)
		assert.Equal(t, ModelID("gemini-2.0-flash-lite"), Gemini20FlashLite)
	})
	
	t.Run("groq model IDs are defined", func(t *testing.T) {
		assert.Equal(t, ModelID("qwen-qwq"), QWENQwq)
		assert.Equal(t, ModelID("meta-llama/llama-4-scout-17b-16e-instruct"), Llama4Scout)
		assert.Equal(t, ModelID("meta-llama/llama-4-maverick-17b-128e-instruct"), Llama4Maverick)
		assert.Equal(t, ModelID("llama-3.3-70b-versatile"), Llama3_3_70BVersatile)
		assert.Equal(t, ModelID("deepseek-r1-distill-llama-70b"), DeepseekR1DistillLlama70b)
	})
}

func TestProviderPopularity(t *testing.T) {
	t.Run("provider popularity is defined", func(t *testing.T) {
		assert.NotEmpty(t, ProviderPopularity)
		
		// Check that all main providers have popularity rankings
		assert.Contains(t, ProviderPopularity, ProviderCopilot)
		assert.Contains(t, ProviderPopularity, ProviderAnthropic)
		assert.Contains(t, ProviderPopularity, ProviderOpenAI)
		assert.Contains(t, ProviderPopularity, ProviderGemini)
		assert.Contains(t, ProviderPopularity, ProviderGROQ)
		assert.Contains(t, ProviderPopularity, ProviderOpenRouter)
		assert.Contains(t, ProviderPopularity, ProviderBedrock)
		assert.Contains(t, ProviderPopularity, ProviderAzure)
		assert.Contains(t, ProviderPopularity, ProviderVertexAI)
		
		// Check popularity order (lower numbers = more popular)
		assert.Equal(t, 1, ProviderPopularity[ProviderCopilot])
		assert.Equal(t, 2, ProviderPopularity[ProviderAnthropic])
		assert.Equal(t, 3, ProviderPopularity[ProviderOpenAI])
	})
}

func TestSupportedModels(t *testing.T) {
	t.Run("supported models map is populated", func(t *testing.T) {
		assert.NotEmpty(t, SupportedModels)
		
		// Check that models from different providers are included
		// We can't check specific models since they're loaded from different files
		// but we can check that the map is not empty
		assert.Greater(t, len(SupportedModels), 0)
	})
	
	t.Run("bedrock model is in supported models", func(t *testing.T) {
		model, exists := SupportedModels[BedrockClaude37Sonnet]
		assert.True(t, exists)
		assert.Equal(t, BedrockClaude37Sonnet, model.ID)
		assert.Equal(t, "Bedrock: Claude 3.7 Sonnet", model.Name)
		assert.Equal(t, ProviderBedrock, model.Provider)
		assert.Equal(t, "anthropic.claude-3-7-sonnet-20250219-v1:0", model.APIModel)
		assert.Equal(t, 3.0, model.CostPer1MIn)
		assert.Equal(t, 15.0, model.CostPer1MOut)
	})
	
	t.Run("models have valid structure", func(t *testing.T) {
		for id, model := range SupportedModels {
			// Each model should have basic required fields
			assert.Equal(t, id, model.ID, "Model ID should match map key")
			assert.NotEmpty(t, model.Name, "Model should have a name")
			assert.NotEmpty(t, model.Provider, "Model should have a provider")
			assert.NotEmpty(t, model.APIModel, "Model should have an API model")
			
			// Cost should be non-negative
			assert.GreaterOrEqual(t, model.CostPer1MIn, 0.0, "Input cost should be non-negative")
			assert.GreaterOrEqual(t, model.CostPer1MOut, 0.0, "Output cost should be non-negative")
			assert.GreaterOrEqual(t, model.CostPer1MInCached, 0.0, "Cached input cost should be non-negative")
			assert.GreaterOrEqual(t, model.CostPer1MOutCached, 0.0, "Cached output cost should be non-negative")
			
			// Context window and max tokens should be reasonable
			if model.ContextWindow > 0 {
				assert.Greater(t, model.ContextWindow, int64(0), "Context window should be positive if set")
			}
			if model.DefaultMaxTokens > 0 {
				assert.Greater(t, model.DefaultMaxTokens, int64(0), "Default max tokens should be positive if set")
			}
		}
	})
}

func TestModelMaps(t *testing.T) {
	t.Run("anthropic models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, AnthropicModels)
		
		// Check that all models have the correct provider
		for _, model := range AnthropicModels {
			assert.Equal(t, ProviderAnthropic, model.Provider)
		}
	})
	
	t.Run("openai models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, OpenAIModels)
		
		// Check that all models have the correct provider
		for _, model := range OpenAIModels {
			assert.Equal(t, ProviderOpenAI, model.Provider)
		}
	})
	
	t.Run("gemini models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, GeminiModels)
		
		// Check that all models have the correct provider
		for _, model := range GeminiModels {
			assert.Equal(t, ProviderGemini, model.Provider)
		}
	})
	
	t.Run("groq models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, GroqModels)
		
		// Check that all models have the correct provider
		for _, model := range GroqModels {
			assert.Equal(t, ProviderGROQ, model.Provider)
		}
	})
	
	t.Run("azure models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, AzureModels)
		
		// Check that all models have the correct provider
		for _, model := range AzureModels {
			assert.Equal(t, ProviderAzure, model.Provider)
		}
	})
	
	t.Run("openrouter models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, OpenRouterModels)
		
		// Check that all models have the correct provider
		for _, model := range OpenRouterModels {
			assert.Equal(t, ProviderOpenRouter, model.Provider)
		}
	})
	
	t.Run("xai models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, XAIModels)
		
		// Check that all models have the correct provider
		for _, model := range XAIModels {
			assert.Equal(t, ProviderXAI, model.Provider)
		}
	})
	
	t.Run("vertexai models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, VertexAIGeminiModels)
		
		// Check that all models have the correct provider
		for _, model := range VertexAIGeminiModels {
			assert.Equal(t, ProviderVertexAI, model.Provider)
		}
	})
	
	t.Run("copilot models map is not empty", func(t *testing.T) {
		assert.NotEmpty(t, CopilotModels)
		
		// Check that all models have the correct provider
		for _, model := range CopilotModels {
			assert.Equal(t, ProviderCopilot, model.Provider)
		}
	})
}

func TestInitFunction(t *testing.T) {
	t.Run("init function populates SupportedModels", func(t *testing.T) {
		// The init function should have run and populated SupportedModels
		// with models from all provider maps
		assert.NotEmpty(t, SupportedModels)
		
		// Check that we have models from different providers
		providers := make(map[ModelProvider]bool)
		for _, model := range SupportedModels {
			providers[model.Provider] = true
		}
		
		// We should have multiple providers represented
		assert.Greater(t, len(providers), 1, "Should have models from multiple providers")
	})
}
