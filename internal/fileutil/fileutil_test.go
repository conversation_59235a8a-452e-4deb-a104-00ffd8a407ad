package fileutil

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSkipHidden(t *testing.T) {
	testCases := []struct {
		name     string
		path     string
		expected bool
	}{
		{
			name:     "hidden file",
			path:     "/path/to/.hidden_file",
			expected: true,
		},
		{
			name:     "hidden directory",
			path:     "/path/to/.hidden_dir",
			expected: true,
		},
		{
			name:     "normal file",
			path:     "/path/to/normal_file.txt",
			expected: false,
		},
		{
			name:     "current directory",
			path:     "/path/to/.",
			expected: false,
		},
		{
			name:     "node_modules directory",
			path:     "/path/to/node_modules/package.json",
			expected: true,
		},
		{
			name:     "pycache directory",
			path:     "/path/to/__pycache__/file.pyc",
			expected: true,
		},
		{
			name:     "git directory",
			path:     "/path/to/.git/config",
			expected: true,
		},
		{
			name:     "opencode directory",
			path:     "/path/to/.opencode/data",
			expected: true,
		},
		{
			name:     "vendor directory",
			path:     "/path/to/vendor/package",
			expected: true,
		},
		{
			name:     "dist directory",
			path:     "/path/to/dist/bundle.js",
			expected: true,
		},
		{
			name:     "build directory",
			path:     "/path/to/build/output",
			expected: true,
		},
		{
			name:     "target directory",
			path:     "/path/to/target/release",
			expected: true,
		},
		{
			name:     "idea directory",
			path:     "/path/to/.idea/workspace.xml",
			expected: true,
		},
		{
			name:     "vscode directory",
			path:     "/path/to/.vscode/settings.json",
			expected: true,
		},
		{
			name:     "bin directory",
			path:     "/path/to/bin/executable",
			expected: true,
		},
		{
			name:     "obj directory",
			path:     "/path/to/obj/Debug",
			expected: true,
		},
		{
			name:     "out directory",
			path:     "/path/to/out/production",
			expected: true,
		},
		{
			name:     "coverage directory",
			path:     "/path/to/coverage/report.html",
			expected: true,
		},
		{
			name:     "tmp directory",
			path:     "/path/to/tmp/temp_file",
			expected: true,
		},
		{
			name:     "temp directory",
			path:     "/path/to/temp/temp_file",
			expected: true,
		},
		{
			name:     "logs directory",
			path:     "/path/to/logs/app.log",
			expected: true,
		},
		{
			name:     "generated directory",
			path:     "/path/to/generated/code.go",
			expected: true,
		},
		{
			name:     "bower_components directory",
			path:     "/path/to/bower_components/package",
			expected: true,
		},
		{
			name:     "jspm_packages directory",
			path:     "/path/to/jspm_packages/package",
			expected: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := SkipHidden(tc.path)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestGlobWithDoublestar(t *testing.T) {
	// Create a temporary directory structure for testing
	tmpDir := t.TempDir()

	// Create test files and directories
	testFiles := []string{
		"file1.txt",
		"file2.go",
		"dir1/file3.txt",
		"dir1/file4.go",
		"dir1/subdir/file5.txt",
		"dir2/file6.py",
		".hidden/file7.txt",
		"node_modules/package.json",
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(tmpDir, file)
		dir := filepath.Dir(fullPath)
		err := os.MkdirAll(dir, 0755)
		require.NoError(t, err)

		err = os.WriteFile(fullPath, []byte("test content"), 0644)
		require.NoError(t, err)
	}

	t.Run("matches all txt files", func(t *testing.T) {
		matches, truncated, err := GlobWithDoublestar("**/*.txt", tmpDir, 0)
		require.NoError(t, err)
		assert.False(t, truncated)

		// Should find txt files but not hidden ones
		var paths []string
		for _, match := range matches {
			paths = append(paths, match)
		}

		assert.Contains(t, paths, filepath.Join(tmpDir, "file1.txt"))
		assert.Contains(t, paths, filepath.Join(tmpDir, "dir1/file3.txt"))
		assert.Contains(t, paths, filepath.Join(tmpDir, "dir1/subdir/file5.txt"))

		// Should not contain hidden files
		assert.NotContains(t, paths, filepath.Join(tmpDir, ".hidden/file7.txt"))
		assert.NotContains(t, paths, filepath.Join(tmpDir, "node_modules/package.json"))
	})

	t.Run("matches go files", func(t *testing.T) {
		matches, truncated, err := GlobWithDoublestar("**/*.go", tmpDir, 0)
		require.NoError(t, err)
		assert.False(t, truncated)

		var paths []string
		for _, match := range matches {
			paths = append(paths, match)
		}

		assert.Contains(t, paths, filepath.Join(tmpDir, "file2.go"))
		assert.Contains(t, paths, filepath.Join(tmpDir, "dir1/file4.go"))
	})

	t.Run("respects limit", func(t *testing.T) {
		matches, truncated, err := GlobWithDoublestar("**/*", tmpDir, 2)
		require.NoError(t, err)

		// Should be truncated when limit is exceeded
		if len(matches) >= 4 { // limit * 2
			assert.True(t, truncated)
		}
	})

	t.Run("handles non-existent pattern", func(t *testing.T) {
		matches, truncated, err := GlobWithDoublestar("**/*.nonexistent", tmpDir, 0)
		require.NoError(t, err)
		assert.False(t, truncated)
		assert.Empty(t, matches)
	})
}

func TestGetRgCmd(t *testing.T) {
	t.Run("returns nil when rg not available", func(t *testing.T) {
		// Save original rgPath
		originalRgPath := rgPath
		defer func() { rgPath = originalRgPath }()

		// Set rgPath to empty to simulate rg not being available
		rgPath = ""

		cmd := GetRgCmd("*.txt")
		assert.Nil(t, cmd)
	})

	t.Run("creates command with pattern", func(t *testing.T) {
		// Save original rgPath
		originalRgPath := rgPath
		defer func() { rgPath = originalRgPath }()

		// Set a fake rg path for testing
		rgPath = "/usr/bin/rg"

		cmd := GetRgCmd("*.txt")
		if cmd != nil {
			assert.Equal(t, "/usr/bin/rg", cmd.Path)
			assert.Contains(t, cmd.Args, "--files")
			assert.Contains(t, cmd.Args, "--glob")
			assert.Contains(t, cmd.Args, "/*.txt")
		}
	})

	t.Run("creates command without pattern", func(t *testing.T) {
		// Save original rgPath
		originalRgPath := rgPath
		defer func() { rgPath = originalRgPath }()

		// Set a fake rg path for testing
		rgPath = "/usr/bin/rg"

		cmd := GetRgCmd("")
		if cmd != nil {
			assert.Equal(t, "/usr/bin/rg", cmd.Path)
			assert.Contains(t, cmd.Args, "--files")
			assert.NotContains(t, cmd.Args, "--glob")
		}
	})
}

func TestGetFzfCmd(t *testing.T) {
	t.Run("returns nil when fzf not available", func(t *testing.T) {
		// Save original fzfPath
		originalFzfPath := fzfPath
		defer func() { fzfPath = originalFzfPath }()

		// Set fzfPath to empty to simulate fzf not being available
		fzfPath = ""

		cmd := GetFzfCmd("query")
		assert.Nil(t, cmd)
	})

	t.Run("creates command with query", func(t *testing.T) {
		// Save original fzfPath
		originalFzfPath := fzfPath
		defer func() { fzfPath = originalFzfPath }()

		// Set a fake fzf path for testing
		fzfPath = "/usr/bin/fzf"

		cmd := GetFzfCmd("test query")
		if cmd != nil {
			assert.Equal(t, "/usr/bin/fzf", cmd.Path)
			assert.Contains(t, cmd.Args, "--filter")
			assert.Contains(t, cmd.Args, "test query")
			assert.Contains(t, cmd.Args, "--read0")
			assert.Contains(t, cmd.Args, "--print0")
		}
	})
}

func TestFileInfo(t *testing.T) {
	t.Run("creates FileInfo struct", func(t *testing.T) {
		now := time.Now()
		fi := FileInfo{
			Path:    "/test/path",
			ModTime: now,
		}

		assert.Equal(t, "/test/path", fi.Path)
		assert.Equal(t, now, fi.ModTime)
	})
}
