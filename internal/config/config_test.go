package config

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/opencode-ai/opencode/internal/llm/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestEnv creates a clean test environment
func setupTestEnv(t *testing.T) (string, func()) {
	t.Helper()

	// Create temporary directory
	tmpDir := t.TempDir()
	homeDir := filepath.Join(tmpDir, "home")
	err := os.MkdirAll(homeDir, 0755)
	require.NoError(t, err)

	// Save original environment
	originalHome := os.Getenv("HOME")
	originalXDG := os.Getenv("XDG_CONFIG_HOME")

	// Set test environment
	os.Setenv("HOME", homeDir)
	os.Setenv("XDG_CONFIG_HOME", "")

	// Reset global config
	Reset()

	cleanup := func() {
		os.Setenv("HOME", originalHome)
		os.Setenv("XDG_CONFIG_HOME", originalXDG)
		Reset()
	}

	return tmpDir, cleanup
}

func TestLoad(t *testing.T) {
	t.Run("loads with minimal config", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		// Create minimal config file
		homeDir := os.Getenv("HOME")
		configPath := filepath.Join(homeDir, ".opencode.json")
		configContent := `{
			"data": {
				"directory": ".opencode"
			},
			"providers": {},
			"agents": {}
		}`

		err := os.WriteFile(configPath, []byte(configContent), 0644)
		require.NoError(t, err)

		cfg, err := Load(tmpDir, false)
		require.NoError(t, err)
		assert.NotNil(t, cfg)
		assert.Equal(t, tmpDir, cfg.WorkingDir)
		assert.Equal(t, ".opencode", cfg.Data.Directory)
	})

	t.Run("loads without config file", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		cfg, err := Load(tmpDir, false)
		require.NoError(t, err)
		assert.NotNil(t, cfg)
		assert.Equal(t, tmpDir, cfg.WorkingDir)
	})

	t.Run("enables debug mode", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		cfg, err := Load(tmpDir, true)
		require.NoError(t, err)
		assert.True(t, cfg.Debug)
	})

	t.Run("returns same instance on multiple calls", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		cfg1, err := Load(tmpDir, false)
		require.NoError(t, err)

		cfg2, err := Load(tmpDir, false)
		require.NoError(t, err)

		assert.Same(t, cfg1, cfg2)
	})
}

func TestGet(t *testing.T) {
	t.Run("returns nil when not loaded", func(t *testing.T) {
		Reset()
		cfg := Get()
		assert.Nil(t, cfg)
	})

	t.Run("returns config after load", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		_, err := Load(tmpDir, false)
		require.NoError(t, err)

		cfg := Get()
		assert.NotNil(t, cfg)
		assert.Equal(t, tmpDir, cfg.WorkingDir)
	})
}

func TestValidate(t *testing.T) {
	t.Run("fails when config not loaded", func(t *testing.T) {
		Reset()
		err := Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "config not loaded")
	})

	t.Run("validates successfully with minimal config", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		_, err := Load(tmpDir, false)
		require.NoError(t, err)

		err = Validate()
		assert.NoError(t, err)
	})
}

func TestWorkingDirectory(t *testing.T) {
	t.Run("panics when config not loaded", func(t *testing.T) {
		Reset()
		assert.Panics(t, func() {
			WorkingDirectory()
		})
	})

	t.Run("returns working directory when loaded", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		_, err := Load(tmpDir, false)
		require.NoError(t, err)

		wd := WorkingDirectory()
		assert.Equal(t, tmpDir, wd)
	})
}

func TestDataDirectory(t *testing.T) {
	t.Run("returns data directory from config", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		_, err := Load(tmpDir, false)
		require.NoError(t, err)

		cfg := Get()
		dataDir := cfg.Data.Directory
		assert.NotEmpty(t, dataDir)
		assert.Equal(t, ".opencode", dataDir)
	})
}

func TestUpdateAgentModel(t *testing.T) {
	t.Run("panics when config not loaded", func(t *testing.T) {
		Reset()
		assert.Panics(t, func() {
			UpdateAgentModel(AgentCoder, models.Claude37Sonnet)
		})
	})

	t.Run("updates agent model successfully", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		// Create config with agents
		homeDir := os.Getenv("HOME")
		configPath := filepath.Join(homeDir, ".opencode.json")
		configContent := `{
			"data": {
				"directory": ".opencode"
			},
			"providers": {
				"anthropic": {
					"apiKey": "test-key"
				}
			},
			"agents": {
				"coder": {
					"model": "claude-3-5-sonnet-20241022",
					"maxTokens": 4000
				}
			}
		}`

		err := os.WriteFile(configPath, []byte(configContent), 0644)
		require.NoError(t, err)

		_, err = Load(tmpDir, false)
		require.NoError(t, err)

		// Update agent model
		err = UpdateAgentModel(AgentCoder, models.Claude37Sonnet)
		require.NoError(t, err)

		// Verify the update
		cfg := Get()
		agent := cfg.Agents[AgentCoder]
		assert.Equal(t, models.Claude37Sonnet, agent.Model)
	})
}

func TestReset(t *testing.T) {
	t.Run("resets global config", func(t *testing.T) {
		tmpDir, cleanup := setupTestEnv(t)
		defer cleanup()

		// Load config
		_, err := Load(tmpDir, false)
		require.NoError(t, err)
		assert.NotNil(t, Get())

		// Reset
		Reset()
		assert.Nil(t, Get())
	})
}

func TestAgentNames(t *testing.T) {
	t.Run("agent constants are defined", func(t *testing.T) {
		assert.Equal(t, AgentName("coder"), AgentCoder)
		assert.Equal(t, AgentName("summarizer"), AgentSummarizer)
		assert.Equal(t, AgentName("task"), AgentTask)
		assert.Equal(t, AgentName("title"), AgentTitle)
	})
}
