package db

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/opencode-ai/opencode/internal/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var ctx = context.Background()

// setupTestConfig creates a minimal config for testing
func setupTestConfig(t *testing.T, tmpDir string) {
	t.Helper()

	// Create a temporary home directory for testing
	homeDir := filepath.Join(tmpDir, "home")
	err := os.MkdirAll(homeDir, 0755)
	require.NoError(t, err)

	// Set HOME environment variable to our temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", homeDir)
	t.Cleanup(func() {
		os.Setenv("HOME", originalHome)
	})

	// Create a minimal config file in the temp home directory
	configPath := filepath.Join(homeDir, ".opencode.json")
	dataDir := filepath.Join(tmpDir, ".opencode")
	configContent := `{
		"data": {
			"directory": "` + dataDir + `"
		},
		"providers": {},
		"agents": {}
	}`

	err = os.WriteFile(configPath, []byte(configContent), 0644)
	require.NoError(t, err)

	// Reset config state for testing
	config.Reset()

	_, err = config.Load(tmpDir, false)
	require.NoError(t, err)
}

func TestConnect(t *testing.T) {
	t.Run("connects successfully with valid config", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		// Test that we can ping the database
		err = db.Ping()
		assert.NoError(t, err)

		// Test that the database file was created
		cfg := config.Get()
		dbPath := filepath.Join(cfg.Data.Directory, "opencode.db")
		_, err = os.Stat(dbPath)
		assert.NoError(t, err)
	})

	t.Run("fails when config not loaded", func(t *testing.T) {
		config.Reset()

		db, err := Connect()
		assert.Error(t, err)
		assert.Nil(t, db)
		assert.Contains(t, err.Error(), "config not loaded")
	})

	t.Run("fails when data directory not set", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		// Clear the data directory
		cfg := config.Get()
		cfg.Data.Directory = ""

		db, err := Connect()
		assert.Error(t, err)
		assert.Nil(t, db)
		assert.Contains(t, err.Error(), "data.dir is not set")
	})

	t.Run("creates data directory if it doesn't exist", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		cfg := config.Get()
		dataDir := cfg.Data.Directory

		// Ensure data directory doesn't exist
		os.RemoveAll(dataDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		// Check that data directory was created
		_, err = os.Stat(dataDir)
		assert.NoError(t, err)
	})

	t.Run("applies database pragmas", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		// Test that foreign keys are enabled
		var foreignKeys int
		err = db.QueryRow("PRAGMA foreign_keys").Scan(&foreignKeys)
		require.NoError(t, err)
		assert.Equal(t, 1, foreignKeys)

		// Test journal mode
		var journalMode string
		err = db.QueryRow("PRAGMA journal_mode").Scan(&journalMode)
		require.NoError(t, err)
		assert.Equal(t, "wal", journalMode)

		// Test page size
		var pageSize int
		err = db.QueryRow("PRAGMA page_size").Scan(&pageSize)
		require.NoError(t, err)
		assert.Equal(t, 4096, pageSize)
	})

	t.Run("runs migrations successfully", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		// Check that tables were created by migrations
		tables := []string{"sessions", "messages", "files"}

		for _, table := range tables {
			var count int
			query := "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?"
			err = db.QueryRow(query, table).Scan(&count)
			require.NoError(t, err)
			assert.Equal(t, 1, count, "Table %s should exist", table)
		}

		// Check that goose_db_version table exists (created by migrations)
		var count int
		err = db.QueryRow("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='goose_db_version'").Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 1, count, "goose_db_version table should exist")
	})
}

func TestNew(t *testing.T) {
	t.Run("creates new Queries instance", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		queries := New(db)
		assert.NotNil(t, queries)
		assert.Implements(t, (*Querier)(nil), queries)
	})
}

func TestPrepare(t *testing.T) {
	t.Run("prepares statements successfully", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		queries, err := Prepare(ctx, db)
		require.NoError(t, err)
		require.NotNil(t, queries)
		defer queries.Close()

		assert.Implements(t, (*Querier)(nil), queries)
	})
}

func TestQueriesClose(t *testing.T) {
	t.Run("closes prepared statements", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		queries, err := Prepare(ctx, db)
		require.NoError(t, err)
		require.NotNil(t, queries)

		err = queries.Close()
		assert.NoError(t, err)
	})
}

func TestWithTx(t *testing.T) {
	t.Run("creates queries with transaction", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		queries := New(db)

		tx, err := db.Begin()
		require.NoError(t, err)
		defer tx.Rollback()

		txQueries := queries.WithTx(tx)
		assert.NotNil(t, txQueries)
		assert.NotSame(t, queries, txQueries)
	})
}

func TestDBTXInterface(t *testing.T) {
	t.Run("sql.DB implements DBTX", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		// Verify that *sql.DB implements DBTX interface
		var _ DBTX = db
	})

	t.Run("sql.Tx implements DBTX", func(t *testing.T) {
		tmpDir := t.TempDir()
		setupTestConfig(t, tmpDir)

		db, err := Connect()
		require.NoError(t, err)
		require.NotNil(t, db)
		defer db.Close()

		tx, err := db.Begin()
		require.NoError(t, err)
		defer tx.Rollback()

		// Verify that *sql.Tx implements DBTX interface
		var _ DBTX = tx
	})
}
