package logging

import (
	"bytes"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// captureLogOutput captures log output for testing
func captureLogOutput(t *testing.T, fn func()) string {
	t.Helper()

	var buf bytes.Buffer

	// Create a new logger that writes to our buffer
	logger := slog.New(slog.NewTextHandler(&buf, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Replace the default logger temporarily
	originalLogger := slog.Default()
	slog.SetDefault(logger)
	defer slog.SetDefault(originalLogger)

	// Execute the function
	fn()

	return buf.String()
}

func TestInfo(t *testing.T) {
	t.Run("logs info message", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Info("test info message")
		})

		assert.Contains(t, output, "test info message")
		assert.Contains(t, output, "level=INFO")
		assert.Contains(t, output, "source=")
	})

	t.Run("logs info message with args", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Info("test message", "key", "value", "number", 42)
		})

		assert.Contains(t, output, "test message")
		assert.Contains(t, output, "key=value")
		assert.Contains(t, output, "number=42")
	})
}

func TestDebug(t *testing.T) {
	t.Run("logs debug message", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Debug("test debug message")
		})

		assert.Contains(t, output, "test debug message")
		assert.Contains(t, output, "level=DEBUG")
		assert.Contains(t, output, "source=")
	})

	t.Run("logs debug message with args", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Debug("debug message", "debug_key", "debug_value")
		})

		assert.Contains(t, output, "debug message")
		assert.Contains(t, output, "debug_key=debug_value")
	})
}

func TestWarn(t *testing.T) {
	t.Run("logs warn message", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Warn("test warn message")
		})

		assert.Contains(t, output, "test warn message")
		assert.Contains(t, output, "level=WARN")
	})

	t.Run("logs warn message with args", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Warn("warn message", "warn_key", "warn_value")
		})

		assert.Contains(t, output, "warn message")
		assert.Contains(t, output, "warn_key=warn_value")
	})
}

func TestError(t *testing.T) {
	t.Run("logs error message", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Error("test error message")
		})

		assert.Contains(t, output, "test error message")
		assert.Contains(t, output, "level=ERROR")
	})

	t.Run("logs error message with args", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			Error("error message", "error_key", "error_value")
		})

		assert.Contains(t, output, "error message")
		assert.Contains(t, output, "error_key=error_value")
	})
}

func TestPersistentLogging(t *testing.T) {
	t.Run("InfoPersist adds persist flag", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			InfoPersist("persistent info")
		})

		assert.Contains(t, output, "persistent info")
		assert.Contains(t, output, "persist=true")
	})

	t.Run("DebugPersist adds persist flag", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			DebugPersist("persistent debug")
		})

		assert.Contains(t, output, "persistent debug")
		assert.Contains(t, output, "persist=true")
	})

	t.Run("WarnPersist adds persist flag", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			WarnPersist("persistent warn")
		})

		assert.Contains(t, output, "persistent warn")
		assert.Contains(t, output, "persist=true")
	})

	t.Run("ErrorPersist adds persist flag", func(t *testing.T) {
		output := captureLogOutput(t, func() {
			ErrorPersist("persistent error")
		})

		assert.Contains(t, output, "persistent error")
		assert.Contains(t, output, "persist=true")
	})
}

func TestRecoverPanic(t *testing.T) {
	t.Run("recovers from panic and creates log file", func(t *testing.T) {
		tmpDir := t.TempDir()
		originalWd, err := os.Getwd()
		require.NoError(t, err)

		// Change to temp directory
		err = os.Chdir(tmpDir)
		require.NoError(t, err)
		defer os.Chdir(originalWd)

		cleanupCalled := false

		func() {
			defer RecoverPanic("test", func() {
				cleanupCalled = true
			})

			panic("test panic")
		}()

		// Check that cleanup was called
		assert.True(t, cleanupCalled)

		// Check that panic log file was created
		files, err := os.ReadDir(tmpDir)
		require.NoError(t, err)

		var panicLogFound bool
		for _, file := range files {
			if strings.HasPrefix(file.Name(), "opencode-panic-test-") && strings.HasSuffix(file.Name(), ".log") {
				panicLogFound = true

				// Read the log file content
				content, err := os.ReadFile(filepath.Join(tmpDir, file.Name()))
				require.NoError(t, err)

				assert.Contains(t, string(content), "Panic in test: test panic")
				assert.Contains(t, string(content), "Stack Trace:")
				break
			}
		}

		assert.True(t, panicLogFound, "Panic log file should be created")
	})

	t.Run("handles panic without cleanup function", func(t *testing.T) {
		tmpDir := t.TempDir()
		originalWd, err := os.Getwd()
		require.NoError(t, err)

		// Change to temp directory
		err = os.Chdir(tmpDir)
		require.NoError(t, err)
		defer os.Chdir(originalWd)

		func() {
			defer RecoverPanic("test", nil)
			panic("test panic without cleanup")
		}()

		// Should not panic even without cleanup function
		// Check that panic log file was created
		files, err := os.ReadDir(tmpDir)
		require.NoError(t, err)

		var panicLogFound bool
		for _, file := range files {
			if strings.HasPrefix(file.Name(), "opencode-panic-test-") {
				panicLogFound = true
				break
			}
		}

		assert.True(t, panicLogFound)
	})
}

func TestGetSessionPrefix(t *testing.T) {
	t.Run("returns first 8 characters", func(t *testing.T) {
		sessionID := "abcdefghijklmnop"
		prefix := GetSessionPrefix(sessionID)
		assert.Equal(t, "abcdefgh", prefix)
	})

	t.Run("handles short session ID", func(t *testing.T) {
		sessionID := "abc"
		prefix := GetSessionPrefix(sessionID)
		assert.Equal(t, "abc", prefix)
	})
}

func TestMessageDir(t *testing.T) {
	t.Run("MessageDir can be set", func(t *testing.T) {
		originalMessageDir := MessageDir
		defer func() { MessageDir = originalMessageDir }()

		MessageDir = "/test/path"
		assert.Equal(t, "/test/path", MessageDir)
	})
}
