package version

import (
	"runtime/debug"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVersion(t *testing.T) {
	t.Run("version is set", func(t *testing.T) {
		// Version should be set to either "unknown" or a valid version
		assert.NotEmpty(t, Version)
		
		// Should be either "unknown" (default) or a version string
		if Version != "unknown" {
			// If not unknown, it should be a non-empty string
			assert.NotEmpty(t, Version)
		}
	})
	
	t.Run("version from build info", func(t *testing.T) {
		// Test that the init function works correctly
		// We can't easily test the actual build info logic without
		// manipulating the build environment, but we can verify
		// that the function doesn't panic and sets a reasonable value
		
		info, ok := debug.ReadBuildInfo()
		if ok {
			mainVersion := info.Main.Version
			if mainVersion != "" && mainVersion != "(devel)" {
				// If we have valid build info, version should be set to it
				// This test might not always pass in test environments
				// but it's useful for integration testing
				t.Logf("Build info version: %s", mainVersion)
				t.Logf("Current version: %s", Version)
			}
		}
		
		// At minimum, Version should be set to something
		assert.NotEmpty(t, Version)
	})
	
	t.Run("version is accessible", func(t *testing.T) {
		// Simple test to ensure the Version variable is accessible
		// and can be read without issues
		v := Version
		assert.NotEmpty(t, v)
		
		// Version should be a string
		assert.IsType(t, "", Version)
	})
}

// TestVersionInit tests the init function behavior
func TestVersionInit(t *testing.T) {
	t.Run("init function sets version", func(t *testing.T) {
		// The init function should have already run when the package was loaded
		// We can't re-run it, but we can verify its effects
		
		// Version should be set to something (either "unknown" or a version string)
		assert.NotEmpty(t, Version)
		
		// Log the current version for debugging
		t.Logf("Version after init: %s", Version)
	})
}

// BenchmarkVersionAccess benchmarks accessing the Version variable
func BenchmarkVersionAccess(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = Version
	}
}
